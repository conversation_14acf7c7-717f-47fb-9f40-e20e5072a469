"""
Personal Trading Configuration
Simple configuration for personal options trading
"""

# API Keys (set these as environment variables for security)
# export FRED_API_KEY="your_fred_api_key_here"
# export OPENROUTER_API_KEY="your_openrouter_api_key_here"
# export ALPHA_VANTAGE_API_KEY="your_alpha_vantage_api_key_here"

# Personal Trading Settings
PERSONAL_SETTINGS = {
    # Account Settings
    'default_account_size': 10000,  # $10K default
    'max_risk_per_trade': 0.1,     # 10% max risk per trade
    'max_options_exposure': 1.0,   # 100% max in options total
    
    # Risk Management
    'profit_target': 0.75,          # Take profits at 75% gain
    'stop_loss': 0.25,              # Stop loss at 25% of premium
    'days_to_expiration_warning': 14,  # Warn when <14 days to expiration
    'days_to_expiration_urgent': 7,    # Urgent when <7 days to expiration
    
    # Trading Preferences
    'preferred_dte': [5, 8, 15, 22, 29, 36, 43, 57, 64, 85, 97, 113, 127],            
    'preferred_symbols': ['SPY'],   # Focus on SPY for simplicity
    'experience_level': 'Beginner', # Beginner, Intermediate, Advanced
    
    # Output Settings
    'show_detailed_analysis': False,  # Keep output simple
    'show_beginner_tips': True,       # Show educational content
    'show_risk_warnings': True,       # Show risk warnings
}

# File Paths
FILES = {
    'portfolio': 'my_portfolio.txt',
    'log': 'personal_trading.log',
    'results': 'trading_results.csv'
}

# Beginner Education Content
BEGINNER_TIPS = {
    'call_options': "Call options give you the right to buy a stock at a specific price. They profit when the stock price goes UP above the strike price.",
    'put_options': "Put options give you the right to sell a stock at a specific price. They profit when the stock price goes DOWN below the strike price.",
    'time_decay': "Options lose value over time, especially in the last 30 days before expiration. This is called time decay or theta.",
    'volatility': "High volatility makes options more expensive. Low volatility makes them cheaper. VIX measures market volatility.",
    'risk_management': "Never risk more than you can afford to lose. Options can expire worthless, so position sizing is crucial."
}

# Risk Warnings
RISK_WARNINGS = {
    'high_risk': "⚠️  WARNING: This trade risks more than recommended for your account size.",
    'expiration': "⚠️  WARNING: This option expires soon. Time decay will accelerate.",
    'volatility': "⚠️  WARNING: High volatility environment. Options are expensive.",
    'concentration': "⚠️  WARNING: Too much of your account is in options. Consider diversifying."
}

def get_personal_setting(key, default=None):
    """Get a personal setting with fallback to default"""
    return PERSONAL_SETTINGS.get(key, default)

def get_risk_warning(warning_type):
    """Get a specific risk warning message"""
    return RISK_WARNINGS.get(warning_type, "⚠️  General risk warning applies.")

def get_beginner_tip(tip_type):
    """Get a specific beginner tip"""
    return BEGINNER_TIPS.get(tip_type, "Continue learning about options trading fundamentals.")
