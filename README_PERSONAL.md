# Personal Options Trading System

## Quick Start Guide

### Daily Workflow (10 minutes before market open):

1. **Update your portfolio** in `my_portfolio.txt`
2. **Run the analysis**: `python "Option Colab.py" personal`
3. **Get ONE clear recommendation** with reasoning
4. **Execute manually** through your broker
5. **Update portfolio** with new position

## Setup Instructions

### 1. Install Required Packages
```bash
pip install pandas numpy yfinance pandas-datareader fredapi requests
```

### 2. Set API Keys (Optional but Recommended)
```bash
# Windows
set FRED_API_KEY=your_fred_api_key_here
set OPENROUTER_API_KEY=your_openrouter_api_key_here

# Mac/Linux
export FRED_API_KEY=your_fred_api_key_here
export OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 3. Update Your Portfolio File
Edit `my_portfolio.txt` with your current:
- Cash balance
- Existing option positions
- Account size

### 4. Run Daily Analysis
```bash
python "Option Colab.py" personal
```

## Portfolio File Format

### Account Section
```
[ACCOUNT]
Cash Balance: 8500.00
Total Account Value: 10000.00
Available for Trading: 8500.00
```

### Positions Section
```
[POSITIONS]
# Symbol | Strike | Expiration | Type | Quantity | Entry Price | Current Price | Entry Date
SPY | 450 | 2025-10-15 | CALL | 1 | 2.50 | 3.20 | 2025-09-20
```

### Settings Section
```
[SETTINGS]
Max Risk Per Trade: 5.0
Account Size: 10000.00
Experience Level: Beginner
```

## Understanding Recommendations

### BUY Recommendations
- **Symbol**: Which option to buy (usually SPY)
- **Strike**: Strike price
- **Expiration**: Expiration date (usually 3-4 weeks out)
- **Type**: CALL (bullish) or PUT (bearish)
- **Max Premium**: Maximum to spend (5% of account)

### SELL Recommendations
- **Which Position**: Specific position to close
- **Reasoning**: Why to close (expiration, profit, loss)
- **Urgency**: How quickly to act

### HOLD Recommendations
- **Reasoning**: Why to wait
- **Market Conditions**: What to watch for
- **Next Steps**: When to reassess

## Risk Management

### Automatic Safeguards
- ✅ Never risk more than 5% per trade
- ✅ Warn when options expire in <14 days
- ✅ Alert when portfolio risk is too high
- ✅ Recommend profit-taking at good levels
- ✅ Suggest cutting losses when appropriate

### Your Responsibilities
- 📝 Keep portfolio file updated
- 💰 Only trade with money you can afford to lose
- 📚 Continue learning about options
- 🎯 Execute trades manually through your broker
- 📊 Track your performance

## Beginner Tips

### Options Basics
- **Call Options**: Profit when stock goes UP
- **Put Options**: Profit when stock goes DOWN
- **Time Decay**: Options lose value over time
- **Volatility**: Affects option prices

### Trading Rules
1. **Start Small**: Begin with 1 contract trades
2. **Take Profits**: Don't be greedy, take 50-100% gains
3. **Cut Losses**: Don't let losses exceed 50% of premium
4. **Avoid Expiration**: Close positions 1 week before expiration
5. **Be Patient**: Wait for high-probability setups

## What Makes This System Special

### 🧠 Sophisticated Analysis
- Multi-agent AI decision making
- Victor Sperandeo technical analysis
- FRED economic indicators
- Advanced risk management
- Market regime detection

### 🎯 Simplified Output
- ONE clear recommendation per day
- Beginner-friendly explanations
- Risk warnings and education
- Position sizing for small accounts

### 💰 Cost Effective
- Fraction of institutional platform costs
- No monthly fees
- Uses free/low-cost data sources

## Troubleshooting

### Common Issues

**"Portfolio file not found"**
- The system will create a template for you
- Update it with your information

**"API key not found"**
- System works without API keys (limited features)
- Set environment variables for full functionality

**"No recommendation generated"**
- Check your portfolio file format
- Ensure market data is available
- Try running during market hours

### Getting Help

1. Check the error messages - they're designed to be helpful
2. Verify your portfolio file format matches the examples
3. Make sure you have internet connection for market data
4. Review the beginner tips and risk warnings

## File Structure

```
Option Colab/
├── Option Colab.py          # Main system (don't modify)
├── my_portfolio.txt         # Your portfolio (update daily)
├── personal_config.py       # Settings (optional to modify)
└── README_PERSONAL.md       # This guide
```

## Important Disclaimers

⚠️  **This is educational software, not financial advice**  
⚠️  **Options trading involves substantial risk of loss**  
⚠️  **Only trade with money you can afford to lose**  
⚠️  **Past performance doesn't guarantee future results**  
⚠️  **Always do your own research before trading**

## Support

This system maintains all the sophisticated analytical capabilities of institutional-grade trading systems while presenting the results in a format suitable for personal trading with small accounts.

The analysis includes:
- Advanced technical analysis (Sperandeo methodology)
- Economic indicator analysis (FRED data)
- Market regime detection
- Risk management
- Multi-agent decision consensus

All of this is simplified into ONE clear daily recommendation with beginner-friendly explanations.

**Happy Trading! 🚀**
